"""Synchronous adapter for async repositories used in performance tests.

This module provides synchronous wrappers around async repositories to enable
their use in performance tests that require synchronous execution patterns,
particularly for threading and multiprocessing scenarios.
"""

import asyncio
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.models.general.user import User
from src.core.repositories.general.user_repository import UserRepository


class SyncUserRepositoryAdapter:
    """Synchronous adapter for UserRepository.

    This adapter wraps the async UserRepository methods and executes them
    synchronously using asyncio.run(). This is specifically designed for
    performance tests that need synchronous execution.
    """

    def __init__(self, sync_session: Session):
        """Initialize the adapter with a synchronous session.

        Note: This creates an AsyncSession from an async engine
        for use with the async repository.
        """
        self.sync_session = sync_session
        # Create an async session using the async engine
        from src.core.database.session import get_async_session_factory
        import asyncio

        # Get async session factory and create session
        async def _create_async_session():
            async_session_factory = await get_async_session_factory()
            return async_session_factory()

        self.async_session = asyncio.run(_create_async_session())
        self.user_repo = UserRepository(self.async_session)

    def create(self, user_data: dict) -> User:
        """Create a user synchronously."""

        async def _create():
            return await self.user_repo.create(user_data)

        return asyncio.run(_create())

    def get_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID synchronously."""

        async def _get_by_id():
            return await self.user_repo.get_by_id(user_id)

        return asyncio.run(_get_by_id())

    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email synchronously."""

        async def _get_by_email():
            return await self.user_repo.get_by_email(email)

        return asyncio.run(_get_by_email())

    def check_email_exists(self, email: str, exclude_user_id: Optional[int] = None) -> bool:
        """Check if email exists synchronously."""

        async def _check_email_exists():
            return await self.user_repo.check_email_exists(email, exclude_user_id)

        return asyncio.run(_check_email_exists())

    def get_by_name(self, name: str) -> Optional[User]:
        """Get user by name synchronously."""

        async def _get_by_name():
            return await self.user_repo.get_by_name(name)

        return asyncio.run(_get_by_name())

    def update(self, user_id: int, update_data: dict) -> Optional[User]:
        """Update user synchronously."""

        async def _update():
            return await self.user_repo.update(user_id, update_data)

        return asyncio.run(_update())

    def delete(self, user_id: int) -> bool:
        """Delete user synchronously."""

        async def _delete():
            return await self.user_repo.delete(user_id)

        return asyncio.run(_delete())

    def get_active_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get active users synchronously."""

        async def _get_active_users():
            return await self.user_repo.get_active_users(skip, limit)

        return asyncio.run(_get_active_users())

    def search_users(self, search_term: str, skip: int = 0, limit: int = 100) -> List[User]:
        """Search users synchronously."""

        async def _search_users():
            return await self.user_repo.search_users(search_term, skip, limit)

        return asyncio.run(_search_users())

    def count_active_users(self) -> int:
        """Count active users synchronously."""

        async def _count_active_users():
            return await self.user_repo.count_active_users()

        return asyncio.run(_count_active_users())

    def close(self):
        """Close the async session."""

        async def _close():
            await self.async_session.close()

        asyncio.run(_close())

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
