"""Scale Performance Tests for Case-Insensitive Email Lookups.

This module tests the performance characteristics of case-insensitive email
lookups at various dataset scales (1K, 10K, 100K users) to ensure the
system maintains responsive performance as the user base grows.

Tests focus on:
1. Query performance impact of func.lower() operations
2. Database index effectiveness at scale
3. Case-insensitive vs case-sensitive lookup performance comparison
4. Memory usage and query optimization at large scales
"""

import pytest
import time
import uuid
import random
import string
from typing import List, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from sqlalchemy.orm import Session
from sqlalchemy import text, func
import psutil
import tracemalloc

from src.core.models.general.user import User
from src.core.repositories.general.user_repository import UserRepository
from src.core.schemas.general.user_schemas import UserCreateSchema
from src.core.services.general.user_service import UserService
from tests.performance.sync_repository_adapter import SyncUserRepositoryAdapter
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.database.session import get_session_factory

pytestmark = [pytest.mark.performance]


class TestEmailLookupScalePerformance:
    """Test suite for email lookup performance at various scales."""

    @pytest.fixture(autouse=True)
    def setup_performance_monitoring(self):
        """Set up performance monitoring for all tests."""
        tracemalloc.start()
        self.start_memory = psutil.Process().memory_info().rss
        yield
        tracemalloc.stop()

    def generate_test_users(self, count: int, prefix: str = "scale_test") -> List[dict]:
        """Generate test user data for scale testing.

        Args:
            count: Number of users to generate
            prefix: Prefix for usernames and emails

        Returns:
            List of user dictionaries for creation
        """
        users = []
        unique_suffix = str(uuid.uuid4())[:8]

        for i in range(count):
            # Generate realistic email variations with different cases
            email_localpart = f"{prefix}_{i}_{unique_suffix}"

            # Add realistic domain variations
            domains = ["example.com", "test.org", "demo.net", "sample.co.uk"]
            domain = random.choice(domains)

            # Create email with mixed case to test normalization
            if i % 4 == 0:
                email = f"{email_localpart.upper()}@{domain.upper()}"
            elif i % 4 == 1:
                email = f"{email_localpart.lower()}@{domain.lower()}"
            elif i % 4 == 2:
                email = f"{email_localpart.capitalize()}@{domain.capitalize()}"
            else:
                email = f"{email_localpart}@{domain}"

            users.append(
                {
                    "name": f"Scale Test User {i} {unique_suffix}",
                    "email": email,
                    "password_hash": f"hashed_password_{i}_{unique_suffix}",
                }
            )

        return users

    def bulk_create_users(self, db_session: Session, users: List[dict]) -> List[User]:
        """Efficiently create users in bulk for testing.

        Args:
            db_session: Database session
            users: List of user data dictionaries

        Returns:
            List of created User objects
        """
        created_users = []

        # Use bulk insert for efficiency
        for user_data in users:
            user = User(**user_data)
            db_session.add(user)
            created_users.append(user)

        db_session.commit()
        return created_users

    @pytest.mark.parametrize("user_count", [1000, 5000, 10000])
    def test_email_lookup_performance_at_scale(self, db_session: Session, user_count: int):
        """Test email lookup performance with various dataset sizes.

        This test measures:
        - Query execution time for case-insensitive lookups
        - Performance degradation as dataset grows
        - Memory usage during large-scale operations
        """
        user_repo = SyncUserRepositoryAdapter(db_session)
        unique_suffix = str(uuid.uuid4())[:8]

        # Generate and create test users
        print(f"\n🔧 Creating {user_count} test users for scale testing...")
        start_time = time.time()

        test_users = self.generate_test_users(user_count, f"scale_{user_count}_{unique_suffix}")
        created_users = self.bulk_create_users(db_session, test_users)

        creation_time = time.time() - start_time
        print(f"✅ Created {user_count} users in {creation_time:.2f}s ({user_count / creation_time:.1f} users/sec)")

        # Test email lookup performance with various query patterns
        sample_users = random.sample(created_users, min(100, len(created_users)))
        lookup_times = []

        print(f"🔍 Testing email lookups with {len(sample_users)} random samples...")

        for user in sample_users:
            # Test case-insensitive lookup with different case variations
            test_emails = [
                user.email.lower(),
                user.email.upper(),
                user.email.capitalize(),
                user.email.swapcase(),
            ]

            for test_email in test_emails:
                start = time.perf_counter()
                found_user = user_repo.get_by_email(test_email)
                end = time.perf_counter()

                lookup_time = (end - start) * 1000  # Convert to milliseconds
                lookup_times.append(lookup_time)

                # Verify lookup correctness
                assert found_user is not None, f"Should find user with email: {test_email}"
                assert found_user.id == user.id, "Should return correct user"
                assert found_user.email.lower() == test_email.lower(), "Email should match (case-insensitive)"

        # Calculate performance metrics
        avg_lookup_time = sum(lookup_times) / len(lookup_times)
        max_lookup_time = max(lookup_times)
        min_lookup_time = min(lookup_times)
        p95_lookup_time = sorted(lookup_times)[int(len(lookup_times) * 0.95)]

        print(f"📊 Email Lookup Performance with {user_count} users:")
        print(f"   Average: {avg_lookup_time:.2f}ms")
        print(f"   Min: {min_lookup_time:.2f}ms")
        print(f"   Max: {max_lookup_time:.2f}ms")
        print(f"   P95: {p95_lookup_time:.2f}ms")

        # Performance assertions based on scale
        if user_count <= 1000:
            assert avg_lookup_time < 50.0, (
                f"Average lookup time should be <50ms for {user_count} users, got {avg_lookup_time:.2f}ms"
            )
            assert p95_lookup_time < 100.0, (
                f"P95 lookup time should be <100ms for {user_count} users, got {p95_lookup_time:.2f}ms"
            )
        elif user_count <= 5000:
            assert avg_lookup_time < 100.0, (
                f"Average lookup time should be <100ms for {user_count} users, got {avg_lookup_time:.2f}ms"
            )
            assert p95_lookup_time < 200.0, (
                f"P95 lookup time should be <200ms for {user_count} users, got {p95_lookup_time:.2f}ms"
            )
        else:  # 10K+ users
            assert avg_lookup_time < 200.0, (
                f"Average lookup time should be <200ms for {user_count} users, got {avg_lookup_time:.2f}ms"
            )
            assert p95_lookup_time < 500.0, (
                f"P95 lookup time should be <500ms for {user_count} users, got {p95_lookup_time:.2f}ms"
            )

        # Memory usage check
        current_memory = psutil.Process().memory_info().rss
        memory_increase = (current_memory - self.start_memory) / (1024 * 1024)  # MB
        print(f"💾 Memory usage increase: {memory_increase:.1f}MB")

        # Memory should not grow excessively with lookup operations
        assert memory_increase < user_count * 0.01, (
            f"Memory usage should not exceed {user_count * 0.01:.1f}MB, got {memory_increase:.1f}MB"
        )

    def test_case_sensitive_vs_case_insensitive_performance_comparison(self, db_session: Session):
        """Compare performance of case-sensitive vs case-insensitive email lookups.

        This test establishes baseline performance differences to understand
        the cost of case-insensitive operations.
        """
        user_repo = SyncUserRepositoryAdapter(db_session)
        unique_suffix = str(uuid.uuid4())[:8]

        # Create test dataset
        test_users = self.generate_test_users(1000, f"comparison_{unique_suffix}")
        created_users = self.bulk_create_users(db_session, test_users)

        sample_users = random.sample(created_users, 50)

        # Test case-sensitive lookups (direct email match)
        case_sensitive_times = []
        for user in sample_users:
            start = time.perf_counter()
            # Direct case-sensitive query
            stmt = text("SELECT * FROM user WHERE email = :email AND is_active = true")
            result = db_session.execute(stmt, {"email": user.email}).fetchone()
            end = time.perf_counter()

            case_sensitive_times.append((end - start) * 1000)
            assert result is not None, "Should find user with exact email match"

        # Test case-insensitive lookups (using func.lower)
        case_insensitive_times = []
        for user in sample_users:
            start = time.perf_counter()
            found_user = user_repo.get_by_email(user.email.upper())  # Test with different case
            end = time.perf_counter()

            case_insensitive_times.append((end - start) * 1000)
            assert found_user is not None, "Should find user with case-insensitive lookup"
            assert found_user.id == user.id, "Should return correct user"

        # Calculate performance comparison
        avg_case_sensitive = sum(case_sensitive_times) / len(case_sensitive_times)
        avg_case_insensitive = sum(case_insensitive_times) / len(case_insensitive_times)
        performance_overhead = ((avg_case_insensitive - avg_case_sensitive) / avg_case_sensitive) * 100

        print(f"📊 Case-Sensitive vs Case-Insensitive Performance Comparison:")
        print(f"   Case-Sensitive Average: {avg_case_sensitive:.2f}ms")
        print(f"   Case-Insensitive Average: {avg_case_insensitive:.2f}ms")
        print(f"   Performance Overhead: {performance_overhead:.1f}%")

        # Performance overhead should be reasonable (less than 100% increase)
        assert performance_overhead < 100.0, (
            f"Case-insensitive overhead should be <100%, got {performance_overhead:.1f}%"
        )

        # Both should be fast enough for production use
        assert avg_case_sensitive < 50.0, f"Case-sensitive lookups should be <50ms, got {avg_case_sensitive:.2f}ms"
        assert avg_case_insensitive < 100.0, (
            f"Case-insensitive lookups should be <100ms, got {avg_case_insensitive:.2f}ms"
        )

    def test_concurrent_email_lookups_performance(self, db_session: Session):
        """Test email lookup performance under concurrent access patterns.

        This test simulates multiple concurrent users performing email lookups
        to identify potential bottlenecks or deadlocks.
        """
        unique_suffix = str(uuid.uuid4())[:8]

        # Create test dataset
        test_users = self.generate_test_users(2000, f"concurrent_{unique_suffix}")
        created_users = self.bulk_create_users(db_session, test_users)

        # Prepare test emails with various case patterns
        test_emails = []
        for user in random.sample(created_users, 100):
            test_emails.extend(
                [
                    user.email.lower(),
                    user.email.upper(),
                    user.email.capitalize(),
                    user.email.swapcase(),
                ]
            )

        # Get the session factory to create sessions in each thread
        session_factory = get_session_factory()

        def perform_email_lookup(email: str) -> Tuple[str, float, bool]:
            """Perform a single email lookup and return timing info."""
            thread_session = session_factory()
            try:
                # Create new repository instance for thread safety
                thread_repo = SyncUserRepositoryAdapter(thread_session)

                start = time.perf_counter()
                found_user = thread_repo.get_by_email(email)
                end = time.perf_counter()

                lookup_time = (end - start) * 1000
                success = found_user is not None

                return email, lookup_time, success
            finally:
                thread_session.close()

        # Test with different concurrency levels
        concurrency_levels = [5, 10, 20]

        for num_threads in concurrency_levels:
            print(f"\n🔄 Testing with {num_threads} concurrent threads...")

            lookup_times = []
            success_count = 0
            start_time = time.time()

            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                # Submit lookup tasks
                futures = [
                    executor.submit(perform_email_lookup, email)
                    for email in random.sample(test_emails, min(100, len(test_emails)))
                ]

                # Collect results
                for future in as_completed(futures):
                    email, lookup_time, success = future.result()
                    lookup_times.append(lookup_time)
                    if success:
                        success_count += 1

            total_time = time.time() - start_time
            avg_lookup_time = sum(lookup_times) / len(lookup_times)
            success_rate = (success_count / len(lookup_times)) * 100
            throughput = len(lookup_times) / total_time

            print(f"   Average lookup time: {avg_lookup_time:.2f}ms")
            print(f"   Success rate: {success_rate:.1f}%")
            print(f"   Throughput: {throughput:.1f} lookups/sec")

            # Performance assertions for concurrent access
            assert success_rate >= 95.0, (
                f"Success rate should be ≥95% with {num_threads} threads, got {success_rate:.1f}%"
            )
            assert avg_lookup_time < 200.0, (
                f"Average lookup time should be <200ms with {num_threads} threads, got {avg_lookup_time:.2f}ms"
            )
            assert throughput > 10.0, (
                f"Throughput should be >10 lookups/sec with {num_threads} threads, got {throughput:.1f}"
            )

    def test_email_uniqueness_check_performance_at_scale(self, db_session: Session):
        """Test email uniqueness check performance with large datasets.

        This test focuses on the check_email_exists method which is called
        during user registration and profile updates.
        """
        user_repo = SyncUserRepositoryAdapter(db_session)
        unique_suffix = str(uuid.uuid4())[:8]

        # Create large test dataset
        test_users = self.generate_test_users(5000, f"uniqueness_{unique_suffix}")
        created_users = self.bulk_create_users(db_session, test_users)

        print(f"\n🔍 Testing email uniqueness checks with {len(created_users)} users...")

        # Test existing email checks (should return True)
        existing_email_times = []
        sample_existing = random.sample(created_users, 50)

        for user in sample_existing:
            # Test with different case variations
            test_emails = [
                user.email.lower(),
                user.email.upper(),
                user.email.capitalize(),
            ]

            for test_email in test_emails:
                start = time.perf_counter()
                exists = user_repo.check_email_exists(test_email)
                end = time.perf_counter()

                existing_email_times.append((end - start) * 1000)
                assert exists is True, f"Should detect existing email: {test_email}"

        # Test non-existing email checks (should return False)
        non_existing_email_times = []
        for i in range(50):
            fake_email = f"nonexistent_{i}_{unique_suffix}@fake.com"

            start = time.perf_counter()
            exists = user_repo.check_email_exists(fake_email)
            end = time.perf_counter()

            non_existing_email_times.append((end - start) * 1000)
            assert exists is False, f"Should not detect non-existing email: {fake_email}"

        # Calculate performance metrics
        avg_existing_time = sum(existing_email_times) / len(existing_email_times)
        avg_non_existing_time = sum(non_existing_email_times) / len(non_existing_email_times)

        print(f"📊 Email Uniqueness Check Performance:")
        print(f"   Existing emails average: {avg_existing_time:.2f}ms")
        print(f"   Non-existing emails average: {avg_non_existing_time:.2f}ms")

        # Performance assertions
        assert avg_existing_time < 100.0, f"Existing email checks should be <100ms, got {avg_existing_time:.2f}ms"
        assert avg_non_existing_time < 100.0, (
            f"Non-existing email checks should be <100ms, got {avg_non_existing_time:.2f}ms"
        )

        # Non-existing email checks might be slightly faster due to early termination
        print(
            f"   Performance difference: {((avg_existing_time - avg_non_existing_time) / avg_non_existing_time) * 100:.1f}%"
        )

    def test_memory_usage_during_large_scale_email_operations(self, db_session: Session):
        """Test memory usage patterns during large-scale email operations.

        This test monitors memory consumption to detect potential memory leaks
        or excessive memory usage during email lookup operations.
        """
        user_repo = SyncUserRepositoryAdapter(db_session)
        unique_suffix = str(uuid.uuid4())[:8]

        # Monitor memory at different stages
        initial_memory = psutil.Process().memory_info().rss / (1024 * 1024)  # MB
        print(f"\n💾 Initial memory usage: {initial_memory:.1f}MB")

        # Create large dataset
        test_users = self.generate_test_users(10000, f"memory_{unique_suffix}")
        created_users = self.bulk_create_users(db_session, test_users)

        after_creation_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        creation_memory_delta = after_creation_memory - initial_memory
        print(f"💾 Memory after creating 10K users: {after_creation_memory:.1f}MB (+{creation_memory_delta:.1f}MB)")

        # Perform intensive email lookup operations
        lookup_start_memory = psutil.Process().memory_info().rss / (1024 * 1024)

        # Perform 1000 email lookups with various patterns
        sample_users = random.sample(created_users, 1000)
        for user in sample_users:
            # Multiple case variations to stress the system
            test_emails = [
                user.email.lower(),
                user.email.upper(),
                user.email.capitalize(),
                user.email.swapcase(),
            ]

            for test_email in test_emails:
                found_user = user_repo.get_by_email(test_email)
                assert found_user is not None, "Should find user"

                # Also test uniqueness check
                exists = user_repo.check_email_exists(test_email)
                assert exists is True, "Should detect existing email"

        after_lookups_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        lookup_memory_delta = after_lookups_memory - lookup_start_memory
        print(f"💾 Memory after 4000 email operations: {after_lookups_memory:.1f}MB (+{lookup_memory_delta:.1f}MB)")

        # Force garbage collection and check for memory leaks
        import gc

        gc.collect()

        after_gc_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        gc_memory_delta = after_lookups_memory - after_gc_memory
        print(f"💾 Memory after garbage collection: {after_gc_memory:.1f}MB (-{gc_memory_delta:.1f}MB)")

        # Memory usage assertions
        assert lookup_memory_delta < 100.0, (
            f"Email operations should not increase memory by >100MB, got {lookup_memory_delta:.1f}MB"
        )
        assert gc_memory_delta >= 0, (
            f"Garbage collection should reduce or maintain memory usage, got {gc_memory_delta:.1f}MB change"
        )

        # Check for potential memory leaks (memory should not grow significantly from lookups alone)
        final_memory_increase = after_gc_memory - lookup_start_memory
        assert final_memory_increase < 50.0, (
            f"Final memory increase should be <50MB after GC, got {final_memory_increase:.1f}MB"
        )

        print(f"✅ Memory usage test completed. Final increase: {final_memory_increase:.1f}MB")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
