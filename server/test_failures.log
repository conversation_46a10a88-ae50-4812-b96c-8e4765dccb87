2025-08-07 16:48:05,365 - ERROR - Test Failed: tests/performance/test_email_lookup_scale_performance.py::TestEmailLookupScalePerformance::test_email_lookup_performance_at_scale[1000]
Failure Details:
src/core/errors/unified_error_handler.py:667: in sync_wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
src/core/database/engine.py:168: in get_async_engine
    raise RuntimeError(
E   RuntimeError: Async database engine not initialized. Call initialize_database_engine() first.

During handling of the above exception, another exception occurred:
tests/performance/test_email_lookup_scale_performance.py:121: in test_email_lookup_performance_at_scale
    user_repo = SyncUserRepositoryAdapter(db_session)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/performance/sync_repository_adapter.py:41: in __init__
    self.async_session = asyncio.run(_create_async_session())
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:195: in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
tests/performance/sync_repository_adapter.py:38: in _create_async_session
    async_session_factory = await get_async_session_factory()
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:634: in async_wrapper
    return await func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/database/session.py:41: in get_async_session_factory
    engine = get_async_engine()
             ^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:688: in sync_wrapper
    raise DatabaseError(
E   src.core.errors.exceptions.DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-08-07 16:48:05,576 - ERROR - Test Failed: tests/performance/test_email_lookup_scale_performance.py::TestEmailLookupScalePerformance::test_email_lookup_performance_at_scale[10000]
Failure Details:
src/core/errors/unified_error_handler.py:667: in sync_wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
src/core/database/engine.py:168: in get_async_engine
    raise RuntimeError(
E   RuntimeError: Async database engine not initialized. Call initialize_database_engine() first.

During handling of the above exception, another exception occurred:
tests/performance/test_email_lookup_scale_performance.py:121: in test_email_lookup_performance_at_scale
    user_repo = SyncUserRepositoryAdapter(db_session)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests/performance/sync_repository_adapter.py:41: in __init__
    self.async_session = asyncio.run(_create_async_session())
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:195: in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py:118: in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
../../../.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/base_events.py:725: in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
tests/performance/sync_repository_adapter.py:38: in _create_async_session
    async_session_factory = await get_async_session_factory()
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:634: in async_wrapper
    return await func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
src/core/database/session.py:41: in get_async_session_factory
    engine = get_async_engine()
             ^^^^^^^^^^^^^^^^^^
src/core/errors/unified_error_handler.py:688: in sync_wrapper
    raise DatabaseError(
E   src.core.errors.exceptions.DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
